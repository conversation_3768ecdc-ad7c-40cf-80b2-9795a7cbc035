import React, { useEffect, useRef, useState } from 'react';
import { EnvelopeIcon, PhoneCallIcon, MapPinIcon } from './common/Icon';

const ContactUs: React.FC = () => {
  const personImageUrl = "/assets/talk.png";

  const sectionRef = useRef<HTMLElement>(null);
  const [isVisible, setIsVisible] = useState(false);
  const [animationPhase, setAnimationPhase] = useState(0);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting && !isVisible) {
          setIsVisible(true);
          // Enhanced staggered animation phases with more sophisticated timing
          setTimeout(() => setAnimationPhase(1), 150);
          setTimeout(() => setAnimationPhase(2), 350);
          setTimeout(() => setAnimationPhase(3), 550);
          setTimeout(() => setAnimationPhase(4), 750);
          setTimeout(() => setAnimationPhase(5), 950);
        }
      },
      { threshold: 0.15, rootMargin: '0px 0px -50px 0px' }
    );

    if (sectionRef.current) {
      observer.observe(sectionRef.current);
    }

    return () => observer.disconnect();
  }, [isVisible]);

  // Enhanced mouse tracking for parallax effects
  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      if (sectionRef.current) {
        const rect = sectionRef.current.getBoundingClientRect();
        const x = (e.clientX - rect.left) / rect.width;
        const y = (e.clientY - rect.top) / rect.height;
        setMousePosition({ x, y });
      }
    };

    if (isVisible) {
      window.addEventListener('mousemove', handleMouseMove);
      return () => window.removeEventListener('mousemove', handleMouseMove);
    }
  }, [isVisible]);

  const contactInfo = [
    {
      icon: <EnvelopeIcon />,
      title: 'Email',
      detail: '<EMAIL>',
      href: 'mailto:<EMAIL>',
    },
    {
      icon: <PhoneCallIcon />,
      title: 'Phone',
      detail: '(0252) 8324 9231',
      href: 'tel:+025283249231',
    },
    {
      icon: <MapPinIcon />,
      title: 'Address',
      detail: 'Jl Harmoni No. 12, Tanah Abang',
    },
  ];

  return (
    <section
      id="contact-us"
      ref={sectionRef}
      className="relative pt-8 md:pt-12 lg:pt-16 pb-4 md:pb-6 lg:pb-8 overflow-hidden"
      aria-labelledby="contact-us-heading"
    >
      {/* Enhanced Animated Background with Parallax */}
      <div className="absolute inset-0 bg-brand-main-red">
        {/* Animated Background Layers */}
        <div
          className={`
            absolute inset-0 transition-all duration-2000 ease-out
            ${isVisible ? 'opacity-100 scale-100' : 'opacity-0 scale-110'}
          `}
          style={{
            background: `radial-gradient(circle at ${mousePosition.x * 100}% ${mousePosition.y * 100}%, rgba(255,255,255,0.1) 0%, transparent 50%)`,
            transform: `translate(${(mousePosition.x - 0.5) * 20}px, ${(mousePosition.y - 0.5) * 20}px)`,
            transition: 'transform 0.3s ease-out'
          }}
        />

        {/* Floating Geometric Shapes */}
        <div className={`
          absolute top-1/4 left-1/4 w-32 h-32 rounded-full
          bg-gradient-to-br from-white/10 to-transparent
          transition-all duration-3000 ease-out
          ${isVisible ? 'opacity-100 translate-y-0 rotate-0' : 'opacity-0 translate-y-16 rotate-45'}
        `} style={{
          transform: `translate(${(mousePosition.x - 0.5) * 30}px, ${(mousePosition.y - 0.5) * 30}px) rotate(${mousePosition.x * 360}deg)`,
          transitionDelay: '200ms'
        }} />

        <div className={`
          absolute top-3/4 right-1/4 w-24 h-24 rounded-2xl
          bg-gradient-to-br from-brand-main-red/10 to-transparent
          transition-all duration-3000 ease-out
          ${isVisible ? 'opacity-100 translate-y-0 rotate-0' : 'opacity-0 translate-y-12 -rotate-45'}
        `} style={{
          transform: `translate(${(mousePosition.x - 0.5) * -25}px, ${(mousePosition.y - 0.5) * -25}px) rotate(${mousePosition.y * -180}deg)`,
          transitionDelay: '400ms'
        }} />

        <div className={`
          absolute top-1/2 right-1/3 w-16 h-16 rounded-full
          bg-gradient-to-br from-white/15 to-transparent
          transition-all duration-3000 ease-out
          ${isVisible ? 'opacity-100 translate-x-0 scale-100' : 'opacity-0 translate-x-8 scale-0'}
        `} style={{
          transform: `translate(${(mousePosition.x - 0.5) * 40}px, ${(mousePosition.y - 0.5) * 40}px) scale(${1 + mousePosition.y * 0.3})`,
          transitionDelay: '600ms'
        }} />
      </div>

      <div className="relative z-10 container mx-auto px-4 sm:px-6 lg:px-8">
        {/* Custom Grid Layout */}
        <div className="parent max-w-6xl mx-auto" style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(4, 1fr)',
          gridTemplateRows: '1fr 1fr',
          gap: '20px'
        }}>

          {/* Enhanced Form Card with Advanced Entrance Animations - div1 */}
          <div className={`
            div1 relative overflow-hidden
            bg-white/80 backdrop-blur-xl
            pt-8 sm:pt-10 px-8 sm:px-10 pb-4 sm:pb-4 rounded-3xl
            shadow-2xl border border-white/20
            transition-all duration-1200 ease-[cubic-bezier(0.16,1,0.3,1)]
            ${isVisible
              ? 'opacity-100 translate-y-0 translate-x-0 scale-100'
              : 'opacity-0 translate-y-12 translate-x-[-20px] scale-95'
            }
          `} style={{
            gridColumn: 'span 2 / span 2',
            gridRow: 'span 2 / span 2',
            transitionDelay: '100ms',
            height: 'calc(100% - 20px)',
            maxHeight: '860px'
          }}>
            {/* Glass Morphism Overlay */}
            <div className="absolute inset-0 bg-gradient-to-br from-white/10 via-transparent to-brand-accent-teal/5 rounded-3xl"></div>

            {/* Enhanced Floating Decorative Elements with Morphing Animations */}
            <div className={`
              absolute top-6 right-6 w-16 h-16 bg-brand-accent-teal/10 rounded-full blur-xl
              transition-all duration-2000 ease-out animate-pulse-slow
              ${animationPhase >= 1 ? 'opacity-100 scale-100 translate-y-0' : 'opacity-0 scale-0 translate-y-[-20px]'}
            `} style={{
              transitionDelay: '300ms',
              transform: `translate(${(mousePosition.x - 0.5) * 10}px, ${(mousePosition.y - 0.5) * 10}px) scale(${1 + mousePosition.x * 0.2})`
            }} />

            <div className={`
              absolute bottom-8 left-6 w-12 h-12 bg-brand-main-red/10 rounded-full blur-lg
              transition-all duration-2000 ease-out animate-pulse-slow animation-delay-400
              ${animationPhase >= 2 ? 'opacity-100 scale-100 translate-x-0' : 'opacity-0 scale-0 translate-x-[-15px]'}
            `} style={{
              transitionDelay: '500ms',
              transform: `translate(${(mousePosition.x - 0.5) * -8}px, ${(mousePosition.y - 0.5) * -8}px) scale(${1 + mousePosition.y * 0.15})`
            }} />

            {/* Additional Morphing Elements */}
            <div className={`
              absolute top-1/2 left-4 w-8 h-8 bg-white/20 rounded-lg blur-md
              transition-all duration-2500 ease-out
              ${animationPhase >= 3 ? 'opacity-100 rotate-0 scale-100' : 'opacity-0 rotate-45 scale-0'}
            `} style={{
              transitionDelay: '700ms',
              transform: `translate(${(mousePosition.x - 0.5) * 12}px, ${(mousePosition.y - 0.5) * 12}px) rotate(${mousePosition.x * 180}deg)`
            }} />

            <div className="relative z-10">
              {/* Enhanced Badge Animation */}
              <div className={`
                transition-all duration-1000 ease-[cubic-bezier(0.16,1,0.3,1)]
                ${animationPhase >= 1
                  ? 'opacity-100 translate-y-0 scale-100 rotate-0'
                  : 'opacity-0 translate-y-6 scale-90 rotate-[-3deg]'
                }
              `} style={{ transitionDelay: '200ms' }}>
                <span className={`
                  inline-block px-4 py-2 bg-gradient-to-r from-brand-accent-teal/20 to-brand-main-red/20
                  backdrop-blur-sm rounded-full text-sm font-semibold text-brand-accent-teal-darker mb-4
                  border border-brand-accent-teal/20 transition-all duration-500 hover:scale-105
                  ${animationPhase >= 1 ? 'animate-pulse-slow' : ''}
                `}>
                  Get in Touch
                </span>
              </div>

              {/* Enhanced Title Animation with Character Stagger Effect */}
              <div className={`
                transition-all duration-1200 ease-[cubic-bezier(0.16,1,0.3,1)]
                ${animationPhase >= 1
                  ? 'opacity-100 translate-y-0 translate-x-0 scale-100'
                  : 'opacity-0 translate-y-8 translate-x-[-10px] scale-95'
                }
              `} style={{ transitionDelay: '350ms' }}>
                <h2 id="contact-us-heading" className={`
                  font-serif text-4xl sm:text-5xl lg:text-6xl font-bold text-neutral-800 mb-4 leading-tight
                  transition-all duration-1000 ease-out min-w-0 w-full
                  ${animationPhase >= 2 ? 'tracking-normal' : 'tracking-wider'}
                `} style={{ minHeight: '7.5rem' }}>
                  <span className={`
                    block transition-all duration-700 ease-out
                    ${animationPhase >= 2 ? 'translate-y-0 opacity-100' : 'translate-y-4 opacity-0'}
                  `} style={{ transitionDelay: '500ms' }}>Let's Chat,</span>
                  <span className={`
                    block whitespace-nowrap transition-all duration-700 ease-out
                    ${animationPhase >= 2 ? 'translate-y-0 opacity-100' : 'translate-y-4 opacity-0'}
                  `} style={{ transitionDelay: '650ms' }}>Reach Out to Us</span>
                </h2>
              </div>

              {/* Enhanced Description Animation */}
              <div className={`
                transition-all duration-1000 ease-[cubic-bezier(0.16,1,0.3,1)]
                ${animationPhase >= 2
                  ? 'opacity-100 translate-y-0 blur-0'
                  : 'opacity-0 translate-y-6 blur-sm'
                }
              `} style={{ transitionDelay: '600ms' }}>
                <p className="text-gray-600 mb-8 text-base sm:text-lg leading-relaxed">
                  Have questions or feedback? We're here to help. Send us a message, and we'll respond within 24 hours.
                </p>
              </div>

              {/* Enhanced Elegant Divider with Morphing Animation */}
              <div className={`
                relative my-8 transition-all duration-1200 ease-[cubic-bezier(0.16,1,0.3,1)]
                ${animationPhase >= 3 ? 'opacity-100 scale-x-100 scale-y-100' : 'opacity-0 scale-x-0 scale-y-50'}
              `} style={{ transitionDelay: '800ms' }}>
                <div className={`
                  h-px bg-gradient-to-r from-transparent via-gray-300 to-transparent
                  transition-all duration-1000 ease-out
                  ${animationPhase >= 3 ? 'opacity-100' : 'opacity-0'}
                `}></div>
                <div className={`
                  absolute inset-0 h-px bg-gradient-to-r from-transparent via-brand-accent-teal/30 to-transparent blur-sm
                  transition-all duration-1000 ease-out
                  ${animationPhase >= 3 ? 'opacity-100 scale-x-100' : 'opacity-0 scale-x-0'}
                `} style={{ transitionDelay: '200ms' }}></div>

                {/* Animated Center Dot */}
                <div className={`
                  absolute left-1/2 top-1/2 transform -translate-x-1/2 -translate-y-1/2
                  w-2 h-2 bg-brand-accent-teal rounded-full
                  transition-all duration-800 ease-out
                  ${animationPhase >= 3 ? 'opacity-100 scale-100' : 'opacity-0 scale-0'}
                `} style={{ transitionDelay: '400ms' }}></div>
              </div>

              <form action="#" method="POST" className={`
                space-y-6 transition-all duration-1000 ease-[cubic-bezier(0.16,1,0.3,1)]
                ${animationPhase >= 3
                  ? 'opacity-100 translate-y-0 scale-100'
                  : 'opacity-0 translate-y-8 scale-98'
                }
              `} style={{ transitionDelay: '900ms' }}>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
                  <div className="group">
                    <label htmlFor="first-name" className="block text-sm font-semibold text-neutral-700 mb-2 transition-colors group-focus-within:text-brand-accent-teal">
                      First Name
                    </label>
                    <div className="relative">
                      <input
                        type="text"
                        name="first-name"
                        id="first-name"
                        autoComplete="given-name"
                        placeholder="Enter your first name"
                        className="w-full px-5 py-4 rounded-2xl bg-white/60 backdrop-blur-sm border border-gray-200/50 focus:ring-2 focus:ring-brand-accent-teal/50 focus:border-brand-accent-teal focus:bg-white/80 transition-all duration-300 placeholder-gray-400 text-gray-800 shadow-lg hover:shadow-xl hover:bg-white/70"
                        aria-required="true"
                      />
                      {/* Glass morphism border effect */}
                      <div className="absolute inset-0 rounded-2xl bg-gradient-to-r from-brand-accent-teal/5 to-brand-main-red/5 opacity-0 group-focus-within:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
                    </div>
                  </div>
                  <div className="group">
                    <label htmlFor="last-name" className="block text-sm font-semibold text-neutral-700 mb-2 transition-colors group-focus-within:text-brand-accent-teal">
                      Last Name
                    </label>
                    <div className="relative">
                      <input
                        type="text"
                        name="last-name"
                        id="last-name"
                        autoComplete="family-name"
                        placeholder="Enter your last name"
                        className="w-full px-5 py-4 rounded-2xl bg-white/60 backdrop-blur-sm border border-gray-200/50 focus:ring-2 focus:ring-brand-accent-teal/50 focus:border-brand-accent-teal focus:bg-white/80 transition-all duration-300 placeholder-gray-400 text-gray-800 shadow-lg hover:shadow-xl hover:bg-white/70"
                        aria-required="true"
                      />
                      <div className="absolute inset-0 rounded-2xl bg-gradient-to-r from-brand-accent-teal/5 to-brand-main-red/5 opacity-0 group-focus-within:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
                    </div>
                  </div>
                </div>
                <div className="group">
                  <label htmlFor="email" className="block text-sm font-semibold text-neutral-700 mb-2 transition-colors group-focus-within:text-brand-accent-teal">
                    Email Address
                  </label>
                  <div className="relative">
                    <input
                      type="email"
                      name="email"
                      id="email"
                      autoComplete="email"
                      placeholder="Enter your email address"
                      className="w-full px-5 py-4 rounded-2xl bg-white/60 backdrop-blur-sm border border-gray-200/50 focus:ring-2 focus:ring-brand-accent-teal/50 focus:border-brand-accent-teal focus:bg-white/80 transition-all duration-300 placeholder-gray-400 text-gray-800 shadow-lg hover:shadow-xl hover:bg-white/70"
                      aria-required="true"
                    />
                    <div className="absolute inset-0 rounded-2xl bg-gradient-to-r from-brand-accent-teal/5 to-brand-main-red/5 opacity-0 group-focus-within:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
                  </div>
                </div>
                <div className="group">
                  <label htmlFor="message" className="block text-sm font-semibold text-neutral-700 mb-2 transition-colors group-focus-within:text-brand-accent-teal">
                    Message
                  </label>
                  <div className="relative">
                    <textarea
                      name="message"
                      id="message"
                      rows={5}
                      placeholder="Tell us about your project or inquiry..."
                      className="w-full px-5 py-4 rounded-2xl bg-white/60 backdrop-blur-sm border border-gray-200/50 focus:ring-2 focus:ring-brand-accent-teal/50 focus:border-brand-accent-teal focus:bg-white/80 transition-all duration-300 resize-none placeholder-gray-400 text-gray-800 shadow-lg hover:shadow-xl hover:bg-white/70"
                      aria-required="true"
                    ></textarea>
                    <div className="absolute inset-0 rounded-2xl bg-gradient-to-r from-brand-accent-teal/5 to-brand-main-red/5 opacity-0 group-focus-within:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
                  </div>
                </div>

                <div className={`
                  transition-all duration-1200 ease-[cubic-bezier(0.16,1,0.3,1)]
                  ${animationPhase >= 4
                    ? 'opacity-100 translate-y-0 scale-100 rotate-0'
                    : 'opacity-0 translate-y-6 scale-95 rotate-[-1deg]'
                  }
                `} style={{ transitionDelay: '1100ms' }}>
                  <button
                    type="submit"
                    className={`
                      group relative w-full overflow-hidden bg-gradient-to-r from-brand-main-red to-brand-main-red-darker
                      text-white font-bold py-4 px-8 rounded-2xl shadow-2xl hover:shadow-3xl
                      transform hover:scale-105 active:scale-95 focus:outline-none focus:ring-4 focus:ring-brand-main-red/30
                      transition-all duration-500 ease-out text-lg border border-white/20
                    `}
                    aria-label="Send your message"
                  >
                    {/* Shimmer Effect */}
                    <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-1000 ease-out"></div>

                    {/* Glass Morphism Background */}
                    <div className="absolute inset-0 bg-white/10 backdrop-blur-sm opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-2xl"></div>

                    {/* Button Content */}
                    <div className="relative z-10 flex items-center justify-center gap-3">
                      <div className="transform transition-all duration-300 group-hover:scale-110 group-hover:rotate-12">
                        <EnvelopeIcon size={22} strokeWidth={2.5} />
                      </div>
                      <span className="font-semibold tracking-wide">Send Message</span>
                    </div>

                    {/* Magnetic pulse effect */}
                    <div className="absolute inset-0 rounded-2xl border-2 border-white/20 opacity-0 group-hover:opacity-100 transition-all duration-300 animate-pulse"></div>
                  </button>
                </div>
              </form>
            </div>
          </div>

          {/* Enhanced Image Card with Advanced 3D Entrance Animations - div2 (Medium Large) */}
          <div className={`
            div2 relative overflow-hidden rounded-3xl shadow-2xl
            transition-all duration-1400 ease-[cubic-bezier(0.16,1,0.3,1)]
            ${isVisible
              ? 'opacity-100 translate-x-0 translate-y-0 scale-100 rotate-0'
              : 'opacity-0 translate-x-12 translate-y-[-8px] scale-90 rotate-[3deg]'
            }
          `} style={{
            transitionDelay: '300ms',
            gridColumn: 'span 2 / span 2',
            gridRow: '1 / 2',
            gridColumnStart: '3',
            height: '490px',
            transform: `perspective(1000px) rotateX(${isVisible ? 0 : -8}deg) rotateY(${isVisible ? 0 : 10}deg)`,
            transformStyle: 'preserve-3d'
          }}>
              {/* Animated Border Glow */}
              <div className={`
                absolute inset-0 rounded-3xl bg-gradient-to-br from-brand-accent-teal/20 via-transparent to-brand-main-red/20
                transition-all duration-2000 ease-out
                ${animationPhase >= 2 ? 'opacity-100 scale-100' : 'opacity-0 scale-110'}
              `} style={{ transitionDelay: '500ms' }} />

              {/* Enhanced Image with Morphing Effects */}
              <img
                src={personImageUrl}
                alt="Professional discussing details"
                className={`
                  w-full h-full object-cover rounded-3xl
                  transition-all duration-1000 ease-out hover:scale-105
                  ${animationPhase >= 1 ? 'filter-none' : 'blur-sm'}
                `}
                style={{
                  transform: `translate(${(mousePosition.x - 0.5) * 5}px, ${(mousePosition.y - 0.5) * 5}px)`,
                  transition: 'transform 0.3s ease-out, filter 1000ms ease-out'
                }}
              />

              {/* Floating Overlay Elements */}
              <div className={`
                absolute top-4 right-4 w-12 h-12 bg-white/20 rounded-full backdrop-blur-sm
                transition-all duration-2000 ease-out
                ${animationPhase >= 3 ? 'opacity-100 scale-100 rotate-0' : 'opacity-0 scale-0 rotate-180'}
              `} style={{
                transitionDelay: '800ms',
                transform: `translate(${(mousePosition.x - 0.5) * 8}px, ${(mousePosition.y - 0.5) * 8}px)`
              }} />
            </div>

          {/* Enhanced Contact Info Card with Advanced Morphing Animations - div3 (Medium Large) */}
          <div className={`
            div3 relative overflow-hidden
            bg-white/80 backdrop-blur-xl border border-white/30
            p-6 lg:p-8 rounded-3xl shadow-2xl
            transition-all duration-1300 ease-[cubic-bezier(0.16,1,0.3,1)]
            ${isVisible
              ? 'opacity-100 translate-x-0 translate-y-0 scale-100 rotate-0'
              : 'opacity-0 translate-x-10 translate-y-4 scale-95 rotate-[2deg]'
            }
          `} style={{
            transitionDelay: '500ms',
            gridColumn: 'span 2 / span 2',
            gridRow: '2 / 3',
            gridColumnStart: '3',
            height: '350px',
            transform: `perspective(1000px) rotateX(${isVisible ? 0 : 6}deg) rotateY(${isVisible ? 0 : -8}deg)`,
            transformStyle: 'preserve-3d'
          }}>
              {/* Glass Morphism Overlay */}
              <div className="absolute inset-0 bg-gradient-to-br from-white/10 via-transparent to-brand-main-red/5 rounded-3xl"></div>

              {/* Enhanced Floating Decorative Elements with Morphing */}
              <div className={`
                absolute top-6 right-6 w-14 h-14 bg-brand-main-red/10 rounded-full blur-lg
                transition-all duration-2500 ease-out animate-pulse-slow
                ${animationPhase >= 3 ? 'opacity-100 scale-100 rotate-0' : 'opacity-0 scale-0 rotate-90'}
              `} style={{
                transitionDelay: '700ms',
                transform: `translate(${(mousePosition.x - 0.5) * 12}px, ${(mousePosition.y - 0.5) * 12}px) rotate(${mousePosition.x * 90}deg)`
              }} />

              <div className={`
                absolute bottom-8 left-6 w-12 h-12 bg-brand-accent-teal/10 rounded-full blur-md
                transition-all duration-2500 ease-out animate-pulse-slow animation-delay-700
                ${animationPhase >= 4 ? 'opacity-100 scale-100 rotate-0' : 'opacity-0 scale-0 rotate-[-90deg]'}
              `} style={{
                transitionDelay: '900ms',
                transform: `translate(${(mousePosition.x - 0.5) * -10}px, ${(mousePosition.y - 0.5) * -10}px) rotate(${mousePosition.y * -90}deg)`
              }} />

              {/* Additional Morphing Element */}
              <div className={`
                absolute top-1/2 right-4 w-6 h-6 bg-white/30 rounded-lg blur-sm
                transition-all duration-2000 ease-out
                ${animationPhase >= 5 ? 'opacity-100 scale-100 rotate-0' : 'opacity-0 scale-0 rotate-45'}
              `} style={{
                transitionDelay: '1100ms',
                transform: `translate(${(mousePosition.x - 0.5) * 15}px, ${(mousePosition.y - 0.5) * 15}px) rotate(${mousePosition.x * 180}deg)`
              }} />

              <div className="relative z-10 h-full flex flex-col justify-center">
                <div className="space-y-4">
                  {contactInfo.map((item, index) => {
                    const Component = item.href ? 'a' : 'div';
                    return (
                      <div
                        key={item.title}
                        className={`
                          transition-all duration-800 ease-[cubic-bezier(0.16,1,0.3,1)]
                          ${animationPhase >= 3
                            ? 'opacity-100 translate-y-0 translate-x-0 scale-100'
                            : 'opacity-0 translate-y-6 translate-x-[-8px] scale-95'
                          }
                        `}
                        style={{ transitionDelay: `${800 + index * 150}ms` }}
                      >
                        <Component
                          href={item.href || undefined}
                          className="group relative flex items-center gap-4 p-3 lg:p-4 rounded-2xl hover:bg-white/60 backdrop-blur-sm transition-all duration-300 ease-in-out w-full border border-transparent hover:border-white/40 hover:shadow-lg"
                          target={item.href && item.href.startsWith('mailto:') ? '_blank' : undefined}
                          rel={item.href && item.href.startsWith('mailto:') ? 'noopener noreferrer' : undefined}
                          aria-label={`${item.title}: ${item.detail}`}
                        >
                          {/* Icon Container with Glass Morphism - Compact */}
                          <div className="relative flex-shrink-0 w-12 h-12 lg:w-14 lg:h-14 bg-gradient-to-br from-brand-accent-teal/20 to-brand-main-red/20 group-hover:from-brand-accent-teal/30 group-hover:to-brand-main-red/30 rounded-2xl flex items-center justify-center transition-all duration-300 backdrop-blur-sm border border-white/20 group-hover:border-white/40 group-hover:scale-110">
                            {/* Glass morphism effect */}
                            <div className="absolute inset-0 bg-white/10 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

                            {React.cloneElement(item.icon as React.ReactElement<any>, {
                              size: 20,
                              className: "relative z-10 text-brand-accent-teal group-hover:text-brand-accent-teal-darker transition-all duration-300 group-hover:scale-110"
                            })}

                            {/* Shimmer effect */}
                            <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-700 ease-out rounded-2xl"></div>
                          </div>

                          <div className="flex-grow">
                            <p className="font-bold text-neutral-800 text-sm lg:text-base mb-1 group-hover:text-brand-accent-teal-darker transition-colors duration-300">{item.title}</p>
                            <p className={`text-xs lg:text-sm text-gray-600 leading-relaxed ${item.href ? 'group-hover:text-brand-main-red transition-colors duration-300' : ''}`}>{item.detail}</p>
                          </div>

                          {/* Hover glow effect */}
                          <div className="absolute inset-0 rounded-2xl bg-gradient-to-r from-brand-accent-teal/5 to-brand-main-red/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                        </Component>
                      </div>
                    );
                  })}
                </div>
              </div>
            </div>

        </div>
      </div>
    </section>
  );
};

export default ContactUs;