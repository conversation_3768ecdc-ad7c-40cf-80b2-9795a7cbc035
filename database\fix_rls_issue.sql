-- Fix for RLS Policy Issue in Le Prestine Authentication
-- Run this SQL in your Supabase SQL Editor to fix the signup issue

-- First, drop the existing trigger if it exists
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;

-- Update the function to handle profile creation with elevated privileges
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO public.user_profiles (id, email, first_name, last_name)
    VALUES (NEW.id, COALESCE(NEW.email, ''), '', '');
    RETURN NEW;
EXCEPTION
    WHEN others THEN
        -- Log the error but don't fail the user creation
        RAISE WARNING 'Could not create user profile for %: %', NEW.id, SQLERRM;
        RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Recreate the trigger
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Create a secure function to update user profiles from the application
CREATE OR REPLACE FUNCTION public.update_user_profile(
    user_id UUID,
    new_first_name TEXT,
    new_last_name TEXT,
    new_phone_number TEXT DEFAULT NULL,
    new_country_code TEXT DEFAULT NULL,
    new_country TEXT DEFAULT NULL
)
RETURNS VOID AS $$
BEGIN
    UPDATE public.user_profiles
    SET
        first_name = new_first_name,
        last_name = new_last_name,
        phone_number = new_phone_number,
        country_code = new_country_code,
        country = new_country,
        updated_at = NOW()
    WHERE id = user_id AND id = auth.uid();
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a function to get user profile (useful for debugging)
CREATE OR REPLACE FUNCTION public.get_user_profile(user_id UUID)
RETURNS TABLE(
    id UUID,
    email TEXT,
    first_name TEXT,
    last_name TEXT,
    phone_number TEXT,
    country_code TEXT,
    country TEXT,
    created_at TIMESTAMPTZ,
    updated_at TIMESTAMPTZ
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        p.id,
        p.email,
        p.first_name,
        p.last_name,
        p.phone_number,
        p.country_code,
        p.country,
        p.created_at,
        p.updated_at
    FROM public.user_profiles p
    WHERE p.id = user_id AND p.id = auth.uid();
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permissions on the functions
GRANT EXECUTE ON FUNCTION public.update_user_profile TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_user_profile TO authenticated;

-- Test the trigger by checking if it exists
SELECT
    trigger_name,
    event_manipulation,
    event_object_table,
    action_statement
FROM information_schema.triggers
WHERE trigger_name = 'on_auth_user_created';

-- Verify the setup
SELECT 'Setup completed successfully! Trigger and functions are ready.' as status;
