-- Fix for RLS Policy Issue in Le Prestine Authentication
-- Run this SQL in your Supabase SQL Editor to fix the signup issue

-- First, drop the existing trigger if it exists
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;

-- Update the function to handle profile creation with elevated privileges
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO public.user_profiles (id, email, first_name, last_name)
    VALUES (NEW.id, COALESCE(NEW.email, ''), '', '');
    RETURN NEW;
EXCEPTION
    WHEN others THEN
        -- Log the error but don't fail the user creation
        RAISE WARNING 'Could not create user profile for %: %', NEW.id, SQLERRM;
        RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Recreate the trigger
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Create a secure function to update user profiles from the application
CREATE OR REPLACE FUNCTION public.update_user_profile(
    user_id UUID,
    new_first_name TEXT,
    new_last_name TEXT,
    new_phone_number TEXT DEFAULT NULL,
    new_country_code TEXT DEFAULT NULL,
    new_country TEXT DEFAULT NULL
)
RETURNS VOID AS $$
BEGIN
    UPDATE public.user_profiles 
    SET 
        first_name = new_first_name,
        last_name = new_last_name,
        phone_number = new_phone_number,
        country_code = new_country_code,
        country = new_country,
        updated_at = NOW()
    WHERE id = user_id AND id = auth.uid();
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission on the function
GRANT EXECUTE ON FUNCTION public.update_user_profile TO authenticated;

-- Verify the setup
SELECT 'Setup completed successfully!' as status;
