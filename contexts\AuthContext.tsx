import React, { createContext, useContext, useEffect, useState } from 'react';
import { User, Session, AuthError } from '@supabase/supabase-js';
import { supabase, UserProfile } from '../lib/supabase';

interface AuthContextType {
  user: User | null;
  profile: UserProfile | null;
  session: Session | null;
  loading: boolean;
  signUp: (email: string, password: string, userData: Partial<UserProfile>) => Promise<{ error: AuthError | null }>;
  signIn: (email: string, password: string) => Promise<{ error: AuthError | null }>;
  signOut: () => Promise<{ error: AuthError | null }>;
  updateProfile: (updates: Partial<UserProfile>) => Promise<{ error: Error | null }>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: React.ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [session, setSession] = useState<Session | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Get initial session
    const getInitialSession = async () => {
      const { data: { session } } = await supabase.auth.getSession();
      setSession(session);
      setUser(session?.user ?? null);
      
      if (session?.user) {
        await fetchUserProfile(session.user.id);
      }
      
      setLoading(false);
    };

    getInitialSession();

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        setSession(session);
        setUser(session?.user ?? null);
        
        if (session?.user) {
          await fetchUserProfile(session.user.id);
        } else {
          setProfile(null);
        }
        
        setLoading(false);
      }
    );

    return () => subscription.unsubscribe();
  }, []);

  const fetchUserProfile = async (userId: string) => {
    try {
      const { data, error } = await supabase
        .from('user_profiles')
        .select('*')
        .eq('id', userId)
        .single();

      if (error && error.code !== 'PGRST116') {
        console.error('Error fetching user profile:', error);
        return;
      }

      setProfile(data);
    } catch (error) {
      console.error('Error fetching user profile:', error);
    }
  };

  const signUp = async (email: string, password: string, userData: Partial<UserProfile>) => {
    try {
      console.log('Starting signup process for:', email);
      console.log('User data:', userData);

      const { data, error } = await supabase.auth.signUp({
        email,
        password,
      });

      if (error) {
        console.error('Supabase auth signup error:', error);
        return { error };
      }

      console.log('Signup successful, user data:', data);

      // Wait for the database trigger to create the basic profile
      if (data.user) {
        console.log('Waiting for database trigger to create profile...');

        // Give the trigger time to create the profile
        await new Promise(resolve => setTimeout(resolve, 2000));

        // Check if profile was created by the trigger
        try {
          const { data: profileData, error: checkError } = await supabase
            .from('user_profiles')
            .select('id, email, first_name, last_name')
            .eq('id', data.user.id)
            .single();

          if (checkError || !profileData) {
            console.error('Profile check failed:', checkError);
            // If profile doesn't exist, try to create it manually
            console.log('Attempting to create profile manually...');

            const { error: insertError } = await supabase
              .from('user_profiles')
              .insert([{
                id: data.user.id,
                email: data.user.email!,
                first_name: userData.first_name || '',
                last_name: userData.last_name || '',
                phone_number: userData.phone_number || null,
                country_code: userData.country_code || null,
                country: userData.country || null,
              }]);

            if (insertError) {
              console.error('Manual profile creation failed:', insertError);
              return {
                error: {
                  message: `Account created but profile setup failed: ${insertError.message}`,
                  name: 'ProfileCreationError'
                } as AuthError
              };
            }
            console.log('Profile created manually');
          } else {
            console.log('Profile found, updating with user data...');

            // Update the profile with the user's data using our secure function
            const { error: updateError } = await supabase.rpc('update_user_profile', {
              user_id: data.user.id,
              new_first_name: userData.first_name || '',
              new_last_name: userData.last_name || '',
              new_phone_number: userData.phone_number || null,
              new_country_code: userData.country_code || null,
              new_country: userData.country || null
            });

            if (updateError) {
              console.error('Error updating user profile:', updateError);
              console.warn('Profile update failed, but signup was successful');
            } else {
              console.log('User profile updated successfully');
            }
          }
        } catch (profileError) {
          console.error('Error handling profile:', profileError);
          // Don't fail the signup if profile handling fails
        }
      }

      return { error: null };
    } catch (error) {
      console.error('Unexpected error during signup:', error);
      return { error: error as AuthError };
    }
  };

  const signIn = async (email: string, password: string) => {
    const { error } = await supabase.auth.signInWithPassword({
      email,
      password,
    });

    return { error };
  };

  const signOut = async () => {
    const { error } = await supabase.auth.signOut();
    return { error };
  };

  const updateProfile = async (updates: Partial<UserProfile>) => {
    if (!user) {
      return { error: new Error('No user logged in') };
    }

    try {
      const { error } = await supabase
        .from('user_profiles')
        .update(updates)
        .eq('id', user.id);

      if (error) {
        return { error: new Error(error.message) };
      }

      // Refresh profile data
      await fetchUserProfile(user.id);
      return { error: null };
    } catch (error) {
      return { error: error as Error };
    }
  };

  const value = {
    user,
    profile,
    session,
    loading,
    signUp,
    signIn,
    signOut,
    updateProfile,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
