<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Le Prestine - Premium Cleaning & Personal Care Products</title>

  <!-- Favicon -->
  <link rel="icon" type="image/png" href="/assets/fav.png">
  <link rel="shortcut icon" type="image/png" href="/assets/fav.png">

  <!-- SEO Meta Tags -->
  <meta name="description" content="Discover Le Prestine's premium cleaning and personal care products. From gentle hand care to powerful cleaning solutions, we offer quality, effective formulas for a spotless home and healthy lifestyle.">
  <meta name="keywords" content="Le Prestine, cleaning products, personal care, laundry detergent, handwash, air fresheners, premium cleaning, gentle formulas, home hygiene, fabric care, stain removal">
  <meta name="author" content="Le Prestine">
  <meta name="robots" content="index, follow">
  <meta name="language" content="English">
  <meta name="revisit-after" content="7 days">

  <!-- Open Graph / Facebook -->
  <meta property="og:type" content="website">
  <meta property="og:url" content="">
  <meta property="og:title" content="Le Prestine - Premium Cleaning & Personal Care Products">
  <meta property="og:description" content="Elevating everyday cleaning into an experience of purity, trust, and care. Discover our complete range of premium cleaning and personal care products with unique, market-tested formulas.">
  <meta property="og:image" content="/assets/hero-image.png">
  <meta property="og:site_name" content="Le Prestine">

  <!-- Twitter -->
  <meta property="twitter:card" content="summary_large_image">
  <meta property="twitter:url" content="">
  <meta property="twitter:title" content="Le Prestine - Premium Cleaning & Personal Care Products">
  <meta property="twitter:description" content="Elevating everyday cleaning into an experience of purity, trust, and care. Discover our complete range of premium cleaning and personal care products.">
  <meta property="twitter:image" content="/assets/hero-image.png">

  <!-- Additional SEO -->
  <meta name="theme-color" content="#DEBFF2">
  <meta name="msapplication-TileColor" content="#DEBFF2">
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="default">
  <meta name="apple-mobile-web-app-title" content="Le Prestine">
  <script src="https://cdn.tailwindcss.com"></script>
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Nunito+Sans:ital,opsz,wght@0,6..12,200..1000;1,6..12,200..1000&family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet">
  <script>
    tailwind.config = {
      theme: {
        extend: {
          fontFamily: {
            'serif': ['Poppins', 'sans-serif'],
            'sans': ['Nunito Sans', 'sans-serif'],
          },
          colors: {
            'brand-maroon': '#6B2135',
            'brand-light-pink': '#FDF7F8',
            'brand-dark-brown': '#4A3B3C',
            'brand-deep-pink': '#A9395C',
            'brand-main-red': '#ed2026',
            'brand-main-red-darker': '#d41c21',
            'brand-main-red-deepdark': '#b9171c',
            'brand-accent-teal': '#09b8a6',
            'brand-accent-teal-darker': '#07a091',
            'brand-primary-lavender': '#DEBFF2',
            'brand-primary-lavender-darker': '#D1A8E8',
            'brand-primary-lavender-light': '#E8D0F5',
            'page-bg-light': '#F0F2F5', // Custom light gray for page backgrounds
          }
        }
      }
    }
  </script>
<style>
  @keyframes marquee {
    0% {
      transform: translateX(0%);
    }
    100% {
      transform: translateX(-50%); /* Because content is duplicated once */
    }
  }

  @keyframes marqueeProfessional {
    0% {
      transform: translate3d(0%, 0, 0) scale3d(1, 1, 1);
      filter: blur(0px) brightness(1) contrast(1);
    }
    20% {
      transform: translate3d(-10%, 0, 0) scale3d(1.01, 1.01, 1);
      filter: blur(0px) brightness(1.02) contrast(1.05);
    }
    40% {
      transform: translate3d(-20%, 0, 0) scale3d(1, 1, 1);
      filter: blur(0px) brightness(1) contrast(1);
    }
    60% {
      transform: translate3d(-30%, 0, 0) scale3d(1.01, 1.01, 1);
      filter: blur(0px) brightness(1.02) contrast(1.05);
    }
    80% {
      transform: translate3d(-40%, 0, 0) scale3d(1, 1, 1);
      filter: blur(0px) brightness(1) contrast(1);
    }
    100% {
      transform: translate3d(-50%, 0, 0) scale3d(1, 1, 1);
      filter: blur(0px) brightness(1) contrast(1);
    }
  }

  .animate-marquee {
    animation-name: marqueeProfessional;
    animation-timing-function: cubic-bezier(0.25, 0.1, 0.25, 1);
    animation-iteration-count: infinite;
    will-change: transform, filter;
    backface-visibility: hidden;
    perspective: 1000px;
  }

  .animate-marquee:hover {
    animation-timing-function: cubic-bezier(0.16, 1, 0.3, 1);
  }

  /* Modern marquee container effects */
  .marquee-container {
    position: relative;
    background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 50%, rgba(255,255,255,0.02) 100%);
    backdrop-filter: blur(20px) saturate(180%);
    border: 1px solid rgba(255,255,255,0.2);
    box-shadow:
      0 8px 32px rgba(0,0,0,0.1),
      inset 0 1px 0 rgba(255,255,255,0.2),
      inset 0 -1px 0 rgba(255,255,255,0.1);
  }

  .marquee-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
    animation: marqueeShine 3s ease-in-out infinite;
    pointer-events: none;
  }

  @keyframes marqueeShine {
    0% { left: -100%; }
    50% { left: 100%; }
    100% { left: 100%; }
  }

  /* Enhanced card hover effects in marquee */
  .marquee-card {
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    transform-origin: center;
    position: relative;
  }

  .marquee-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255,255,255,0.1), transparent);
    opacity: 0;
    transition: opacity 0.3s ease;
    border-radius: inherit;
    pointer-events: none;
  }

  .marquee-card:hover::before {
    opacity: 1;
  }

  .marquee-card:hover {
    transform: translateY(-8px) scale(1.03);
    filter: brightness(1.1) saturate(1.2);
    box-shadow:
      0 20px 40px rgba(0,0,0,0.15),
      0 8px 16px rgba(0,0,0,0.1),
      inset 0 1px 0 rgba(255,255,255,0.3);
  }

  /* Enhanced animation performance optimizations */
  .animate-marquee * {
    backface-visibility: hidden;
    perspective: 1000px;
    will-change: transform;
    transform-style: preserve-3d;
  }

  .animation-paused {
    animation-play-state: paused !important; /* !important to override Tailwind's potential overrides */
  }

  /* Professional card hover effects with GPU acceleration */
  .marquee-card {
    will-change: transform, box-shadow, filter;
    backface-visibility: hidden;
    transform-style: preserve-3d;
    transition: transform 0.3s cubic-bezier(0.16, 1, 0.3, 1),
                box-shadow 0.3s cubic-bezier(0.16, 1, 0.3, 1),
                filter 0.3s cubic-bezier(0.16, 1, 0.3, 1);
  }

  .marquee-card:hover {
    transform: translate3d(0, -8px, 0) scale3d(1.02, 1.02, 1);
    filter: brightness(1.05) contrast(1.1);
  }

  /* Smooth scroll performance */
  .marquee-container {
    contain: layout style paint;
    transform: translateZ(0);
  }
  body {
    font-family: 'Nunito Sans', sans-serif; /* Default to Nunito Sans for the app */
  }

  @keyframes fadeIn {
    from { opacity: 0; transform: translateY(15px); }
    to { opacity: 1; transform: translateY(0); }
  }
  .animate-fadeIn {
    animation: fadeIn 0.7s ease-out forwards;
  }
  .animation-delay-200 {
    animation-delay: 0.2s;
  }
  .animation-delay-300 {
    animation-delay: 0.3s;
  }

  /* Glassmorphism support */
  .backdrop-blur-xl {
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
  }

  /* Enhanced glassmorphism effect */
  .glass-morphism {
    background: rgba(9, 184, 166, 0.1);
    backdrop-filter: blur(20px) saturate(180%);
    -webkit-backdrop-filter: blur(20px) saturate(180%);
    box-shadow: 0 8px 32px 0 rgba(9, 184, 166, 0.2);
  }

  /* Hide scrollbar for mobile horizontal scroll */
  .scrollbar-hide {
    -ms-overflow-style: none;  /* Internet Explorer 10+ */
    scrollbar-width: none;  /* Firefox */
  }
  .scrollbar-hide::-webkit-scrollbar {
    display: none;  /* Safari and Chrome */
  }

  /* Hero Section Animations */
  @keyframes parallaxFloat {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-10px) rotate(1deg); }
  }

  @keyframes slideInLeft {
    from {
      opacity: 0;
      transform: translateX(-50px) translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateX(0) translateY(0);
    }
  }

  @keyframes slideInRight {
    from {
      opacity: 0;
      transform: translateX(50px) translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateX(0) translateY(0);
    }
  }

  @keyframes scaleIn {
    from {
      opacity: 0;
      transform: scale(0.9) translateY(20px);
    }
    to {
      opacity: 1;
      transform: scale(1) translateY(0);
    }
  }

  @keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
  }

  @keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.8; }
  }

  .animate-parallax-float {
    animation: parallaxFloat 6s ease-in-out infinite;
  }

  .animate-slide-in-left {
    animation: slideInLeft 0.8s ease-out forwards;
  }

  .animate-slide-in-right {
    animation: slideInRight 0.8s ease-out forwards;
  }

  .animate-scale-in {
    animation: scaleIn 0.6s ease-out forwards;
  }

  .animate-shimmer {
    animation: shimmer 2s ease-in-out infinite;
  }

  .animate-pulse-slow {
    animation: pulse 3s ease-in-out infinite;
  }

  .animation-delay-100 { animation-delay: 0.1s; }
  .animation-delay-400 { animation-delay: 0.4s; }
  .animation-delay-500 { animation-delay: 0.5s; }
  .animation-delay-600 { animation-delay: 0.6s; }
  .animation-delay-700 { animation-delay: 0.7s; }
  .animation-delay-800 { animation-delay: 0.8s; }

  /* Enhanced glass morphism for hero */
  .hero-glass-morphism {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px) saturate(180%);
    -webkit-backdrop-filter: blur(20px) saturate(180%);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  /* Parallax container */
  .parallax-container {
    transform-style: preserve-3d;
    perspective: 1000px;
  }

  /* Scroll-triggered animations */
  .scroll-animate {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.8s ease-out;
  }

  .scroll-animate.in-view {
    opacity: 1;
    transform: translateY(0);
  }

  /* Simple Category Card Fade-in Animation */
  @keyframes categoryFadeIn {
    0% {
      opacity: 0;
    }
    100% {
      opacity: 1;
    }
  }

  /* Simple fade-in animation class */
  .category-fade-in {
    animation: categoryFadeIn 0.6s ease-out forwards;
    opacity: 0;
  }

  /* Staggered Animation Delays for Category Cards */
  .category-delay-0 { animation-delay: 0ms; }
  .category-delay-1 { animation-delay: 150ms; }
  .category-delay-2 { animation-delay: 300ms; }
  .category-delay-3 { animation-delay: 450ms; }
  .category-delay-4 { animation-delay: 600ms; }
  .category-delay-5 { animation-delay: 750ms; }
  .category-delay-6 { animation-delay: 900ms; }
  .category-delay-7 { animation-delay: 1050ms; }
  .category-delay-8 { animation-delay: 1200ms; }

  /* Section entrance animation */
  .category-section-entrance {
    animation: fadeIn 1s ease-out forwards;
    opacity: 0;
  }

  /* Modern Professional Custom Scrollbar Styles */
  .custom-dropdown-scrollbar::-webkit-scrollbar {
    width: 6px;
  }

  .custom-dropdown-scrollbar::-webkit-scrollbar-track {
    background: transparent;
    border-radius: 10px;
  }

  .custom-dropdown-scrollbar::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, rgba(9, 184, 166, 0.4), rgba(9, 184, 166, 0.6));
    border-radius: 10px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    box-shadow: 0 2px 4px rgba(9, 184, 166, 0.1);
  }

  .custom-dropdown-scrollbar::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, rgba(9, 184, 166, 0.6), rgba(9, 184, 166, 0.8));
    transform: scaleX(1.2);
    box-shadow: 0 4px 8px rgba(9, 184, 166, 0.2);
  }

  .custom-dropdown-scrollbar::-webkit-scrollbar-thumb:active {
    background: linear-gradient(135deg, rgba(9, 184, 166, 0.8), rgba(9, 184, 166, 1));
    transform: scaleX(1.3);
    box-shadow: 0 6px 12px rgba(9, 184, 166, 0.3);
  }

  .custom-dropdown-scrollbar::-webkit-scrollbar-corner {
    background: transparent;
  }

  /* Enhanced scrollbar with glass morphism effect */
  .custom-dropdown-scrollbar::-webkit-scrollbar-thumb {
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
  }

  /* Smooth scrolling behavior */
  .custom-dropdown-scrollbar {
    scroll-behavior: smooth;
  }

  /* Enhanced custom scrollbar for phone input dropdown */
  .custom-scrollbar {
    scrollbar-width: thin;
    scrollbar-color: rgba(156, 163, 175, 0.5) transparent;
  }

  .custom-scrollbar::-webkit-scrollbar {
    width: 6px;
  }

  .custom-scrollbar::-webkit-scrollbar-track {
    background: transparent;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb {
    background-color: rgba(156, 163, 175, 0.5);
    border-radius: 3px;
    transition: background-color 0.2s ease;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background-color: rgba(156, 163, 175, 0.8);
  }
</style>
<script type="importmap">
{
  "imports": {
    "react/": "https://esm.sh/react@^19.1.0/",
    "react": "https://esm.sh/react@^19.1.0",
    "react-dom/": "https://esm.sh/react-dom@^19.1.0/",
    "@/": "./"
  }
}
</script>
<link rel="stylesheet" href="/index.css">
</head>
<body class="bg-gray-100 font-sans">
  <div id="root"></div>
  <script type="module" src="/index.tsx"></script>
</body>
</html>